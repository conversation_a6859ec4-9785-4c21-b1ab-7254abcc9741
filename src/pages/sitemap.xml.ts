// src/pages/sitemap.xml.ts
import fs from 'fs'
import path from 'path'

export async function GET() {
	const baseUrl = 'https://kuberns.com/blogs'
	const blogDir = path.join(process.cwd(), 'src/content/blog')

	const files = fs.readdirSync(blogDir).filter((file) => {
		const fullPath = path.join(blogDir, file)
		return fs.statSync(fullPath).isFile() && file.endsWith('.mdx')
	})
	const blogUrls = files
		.filter((file) => file.endsWith('.mdx'))
		.map((file) => {
			const slug = path.basename(file, '.mdx').toLowerCase()
			return {
				loc: `${baseUrl}/post/${slug}`,
				lastmod: new Date().toISOString().split('T')[0], // or read frontmatter for actual date
				changefreq: 'weekly',
				priority: 0.7
			}
		})

	const staticUrls = [
		{
			loc: `${baseUrl}/`,
			lastmod: '2025-04-15',
			changefreq: 'weekly',
			priority: 0.9
		}
	]

	const allUrls = [...staticUrls, ...blogUrls]

	const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
  <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  ${allUrls
			.map(
				(url) => `<url>
    <loc>${url.loc}</loc>
    <lastmod>${url.lastmod}</lastmod>
    <changefreq>${url.changefreq}</changefreq>
    <priority>${url.priority}</priority>
  </url>`
			)
			.join('\n')}
  </urlset>`

	return new Response(sitemap, {
		headers: {
			'Content-Type': 'application/xml'
		}
	})
}
