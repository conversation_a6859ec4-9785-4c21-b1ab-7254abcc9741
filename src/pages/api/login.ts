import type { APIRoute } from 'astro'

export const POST: APIRoute = async ({ request, cookies, redirect }) => {
	let formData
	const contentType = request.headers.get('content-type')
	formData = await request.json()

	//
	//   if (contentType?.includes("application/json")) {
	//     // Handle JSON request (from fetch())
	//     formData = await request.json();
	//   } else {
	//     return new Response("Unsupported Content-Type", { status: 400 });
	//   }

	const { username, password } = formData

	// Hardcoded valid user (replace this with real authentication)
	const validUsers = [{ username: 'admin', password: 'secret123' }]

	const isValidUser = validUsers.some(
		(user) => user.username === username && user.password === password
	)

	if (!isValidUser) {
		return new Response(JSON.stringify({ error: 'Invalid credentials' }), { status: 401 })
	}

	cookies.set('authToken', 'admin-secret', {
		path: '/',
		httpOnly: true, // JavaScript cannot access it (for security)
		secure: true, // Set to true in production (HTTPS only)
		sameSite: 'lax', // Allows cookies to be sent with navigation requests
		maxAge: 60 * 60 * 24 // 1 day expiration
	})

	return new Response(JSON.stringify({ success: true }), {
		status: 200,
		headers: {
			'Content-Type': 'application/json',
			'Set-Cookie': `authToken=admin-secret; Path=/; HttpOnly; SameSite=Lax; Max-Age=86400`
		}
	})
}
