---
const searchParams = Astro.url.searchParams
const error = searchParams.get('error')
---

<!doctype html>
<html lang='en'>
	<head>
		<meta charset='UTF-8' />
		<meta name='viewport' content='width=device-width, initial-scale=1.0' />
		<title>Login</title>
		<style>
			body {
				font-family: Arial, sans-serif;
				text-align: center;
				margin: 50px;
			}
			form {
				display: inline-block;
				text-align: left;
				background: #f4f4f4;
				padding: 20px;
				border-radius: 8px;
			}
			label,
			input {
				display: block;
				margin-bottom: 10px;
			}
			.error {
				color: red;
			}
		</style>
	</head>
	<body>
		<h2>Admin Login</h2>
		{error && <p class='error'>Invalid credentials. Try again.</p>}

		<form id='loginForm'>
			<label for='username'>Username</label>
			<input type='text' id='username' name='username' required />

			<label for='password'>Password</label>
			<input type='password' id='password' name='password' required />

			<button type='submit'>Login</button>
		</form>

		<script>
			const loginForm = document.getElementById('loginForm')
			if (loginForm) {
				loginForm.addEventListener('submit', async function (event) {
					event.preventDefault()

					const usernameElement = document.getElementById('username')
					const username = usernameElement ? (usernameElement as HTMLInputElement).value : ''
					const passwordElement = document.getElementById('password')
					const password = passwordElement ? (passwordElement as HTMLInputElement).value : ''

					const response = await fetch('/api/login', {
						method: 'POST',
						headers: {
							'Content-Type': 'application/json'
						},
						credentials: 'include', // ✅ Ensures cookies are sent and received

						body: JSON.stringify({ username, password })
					})

					if (response.ok) {
						window.location.href = '/admin' // ✅ Redirect manually
					} else {
						alert('Login failed!')
					}
				})
			}
		</script>
	</body>
</html>
