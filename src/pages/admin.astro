---
const isDev = import.meta.env.MODE === 'development'
---

<!doctype html>
<html lang='en'>
	<head>
		<meta charset='UTF-8' />
		<meta name='viewport' content='width=device-width, initial-scale=1.0' />
		<title>TinaCMS Admin</title>

		{
			isDev ? (
				<>
					<script type='module'>
						import RefreshRuntime from 'http://localhost:4001/@react-refresh'
						RefreshRuntime.injectIntoGlobalHook(window) 
						window.$RefreshReg$ = () => {}
						window.$RefreshSig$ = () => (type) => type 
						indow._vite_plugin_react_preamble_installed_
						= true
					</script>
					<script type='module' src='http://localhost:4001/@vite/client' />
					<script
						type='module'
						src='http://localhost:4001/src/main.tsx'
						onerror='handleLoadError()'
					/>
				</>
			) : (
				<>
					<script
						type='module'
						src={`${import.meta.env.BASE_URL}/admin`}
						onerror='handleLoadError()'
					/>
				</>
			)
		}

		<script>
			function handleLoadError() {
				const rootElement = document.getElementById('root')
				if (rootElement) {
					rootElement.innerHTML = `
            <style type="text/css">
              body { font-family: sans-serif; font-size: 16px; line-height: 1.4; color: #333; background-color: #f5f5f5; }
              #no-assets-placeholder { max-width: 600px; margin: 0 auto; padding: 40px; text-align: center; background-color: #fff; box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.1); }
              h1 { font-size: 24px; margin-bottom: 20px; }
              p { margin-bottom: 10px; }
              a { color: #0077cc; text-decoration: none; }
              a:hover { text-decoration: underline; }
            </style>
            <div id="no-assets-placeholder">
              <h1>Failed loading TinaCMS assets</h1>
              <p>Your TinaCMS configuration may be misconfigured, and we could not load the assets for this page.</p>
              <p>Please visit <a href="https://tina.io/docs/tina-cloud/faq/#how-do-i-resolve-failed-loading-tinacms-assets-error">this doc</a> for help.</p>
            </div>`
				}
			}
		</script>
	</head>
	<body class='tina-tailwind'>
		<div id='root'></div>
	</body>
</html>
