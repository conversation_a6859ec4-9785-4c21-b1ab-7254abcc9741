---
import type { CollectionEntry } from 'astro:content'
import FormattedDate from '@/components/FormattedDate'
import BaseLayout from '@/layouts/BaseLayout'
import Tag from '@/components/Tag'
import type { MarkdownHeading } from 'astro'
import { Image } from 'astro:assets'

type Props = {
	id: CollectionEntry<'blog'>['id']
	data: CollectionEntry<'blog'>['data']
	headings: MarkdownHeading[]
	readTime: string
}

const { data, readTime, headings, id } = Astro.props
const { title, description, pubDate, heroImage, tags } = data

const articleDate = pubDate.toISOString()
---

<BaseLayout
	title={title}
	description={description}
	image={heroImage?.src}
	articleDate={articleDate}
>
	<article class='min-w-full md:py-4 sm:max-w-none md:max-w-none'>
		<header class='mb-3 flex flex-col justify-center items-center gap-6'>
			<div class='flex flex-col gap-2'>
				<div class='flex items-center justify-center gap-x-1'>
					<p class='text-center text-sm text-opacity-50'>
						Published <FormattedDate date={pubDate} />
					</p>
					<p class='text-center text-sm text-opacity-50 font-bold'>
						- {readTime}
					</p>
				</div>
				<h1 class='text-center text-4xl md:text-6xl md:pb-2.5 font-semibold'>
					{title}
				</h1>
			</div>

			<div class='flex flex-wrap justify-center items-center gap-2 gap-y-4 md:gap-5'>
				{tags.map((tag) => <Tag tag={tag} />)}
			</div>
		</header>

		<>
			{
				heroImage && (
					<Image
						src={heroImage}
						width={1000}
						height={500}
						quality={100}
						format='jpg'
						loading='eager'
						class='rounded-md w-full max-h-[300px]  md:max-h-[500px] my-8 object-cover'
						alt={`img of ${title}`}
					/>
				)
			}
		</>

		<hr />

		<div>
			<slot />
		</div>
	</article>
</BaseLayout>

<script>
	const fnObserver = () => {
		function handleIntersection(
			entries: IntersectionObserverEntry[],
			observer: IntersectionObserver
		) {
			entries.forEach((entry) => {
				const index = document.querySelector(`aside a[href="#${entry.target.id}"]`)

				if (entry.isIntersecting) {
					index?.classList.remove('bg-slate-200', 'dark:bg-slate-800') // remove bg
					index?.classList.add(
						'bg-[#2758D1]',
						'dark:[#2758D1]',
						'text-white',
						'font-bold',
						'transition-colors',
						'duration-200'
					)
				} else {
					index?.classList.add('bg-slate-200', 'dark:bg-slate-800') // add bg
					index?.classList.remove(
						'bg-[#2758D1]',
						'dark:bg-[#2758D1]',
						'text-white',
						'font-bold',
						'transition-colors',
						'duration-200'
					)
				}
			})
		}

		const headings = document.querySelectorAll(
			'div.prose h1, div.prose h2, div.prose h3, div.prose h4, div.prose h5, div.prose h6'
		)

		const options = {
			root: null,
			rootMargin: ' 15% 0px 0% 0px ',
			threshold: 1
		}

		const observer = new IntersectionObserver(handleIntersection, options)

		headings.forEach((heading) => {
			observer.observe(heading)
		})
	}
	fnObserver()
	document.addEventListener('astro:after-swap', fnObserver)
</script>
