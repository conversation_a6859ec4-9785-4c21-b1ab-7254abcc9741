---
import type { HTMLAttributes } from 'astro/types'
import { cn } from '@/utils'
type Props = HTMLAttributes<'a'>

const { href, class: className, target, ...props } = Astro.props

const { pathname } = Astro.url
const isActive = href === pathname || href === pathname.replace(/\/$/, '')

const isExternal = href.startsWith('http')
const isInternal = href.startsWith('/')
const join = (base, path) =>
	base.endsWith('/')
		? path.startsWith('/')
			? base + path.slice(1)
			: base + path
		: path.startsWith('/')
			? base + path
			: base + '/' + path
const finalHref = isExternal ? href : isInternal ? join(import.meta.env.BASE_URL, href) : href
---

<a
	href={finalHref}
	class={cn(isActive ? 'text-opacity-100' : 'text-opacity-60', className)}
	rel='noopener noreferrer '
	target={target ? target : isExternal ? '_blank' : undefined}
	aria-label={Astro.props['aria-label']}
	{...props}
>
	<slot />
</a>
